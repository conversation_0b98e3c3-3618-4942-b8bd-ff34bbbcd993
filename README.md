# remoty-ai-service

A Flask web application for managing image timelines with AI processing capabilities.

## Features

- User authentication and registration
- Company management
- Timeline creation and management
- Image upload and AI processing
- Timeline evaluation
- Responsive UI with Bootstrap 5

## Business Requirements

- Timeline management for images
- AI processing of image content
- Time mark selection for images
- Timeline viewing by user, company, and day
- CRUD operations for timeline management
- Timeline evaluation with markdown support
- User-friendly interface

## Installation

1. Clone the repository:
```
git clone <repository-url>
cd timeline-manager
```

2. Create a virtual environment and activate it:
```
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```
pip install -r requirements.txt
```

4. Set up environment variables:
```
cp .env.example .env
# Edit .env with your configuration
```

5. Initialize the database:
```
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
flask init-db  # Add sample data
```

6. Create an admin user:
```
flask create-admin <username> <email> <password>
```

7. Run the application:
```
flask run
```

8. Access the application at http://localhost:5000

## Project Structure

```
timeline-manager/
├── app/
│   ├── models/
│   │   ├── __init__.py
│   │   ├── user.py
│   │   ├── company.py
│   │   ├── timeline.py
│   │   └── image.py
│   ├── routes/
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   ├── timeline.py
│   │   └── image.py
│   ├── templates/
│   │   ├── base.html
│   │   ├── auth/
│   │   ├── timeline/
│   │   └── image/
│   ├── static/
│   │   ├── css/
│   │   ├── js/
│   │   └── uploads/
│   ├── utils/
│   │   ├── __init__.py
│   │   └── ai_processor.py
│   └── __init__.py
├── migrations/
├── app.py
├── config.py
├── requirements.txt
├── .env
└── README.md
```

## Technologies Used

- Flask - Web framework
- SQLAlchemy - ORM for database
- Flask-Login - User authentication
- Flask-WTF - Form handling
- Pillow - Image processing
- Bootstrap 5 - UI framework
- Markdown2 - Markdown processing
- Flask-Migrate - Database migrations

## License

This project is licensed under the MIT License - see the LICENSE file for details.
