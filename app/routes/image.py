from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, jsonify
from flask_login import login_required, current_user
from app import db
from app.models.image import Image
from app.models.timeline import Timeline
from werkzeug.utils import secure_filename
from flask_wtf import FlaskForm
from flask_wtf.file import File<PERSON>ield, FileRequired, FileAllowed
from wtforms import StringField, TextAreaField, TimeField, SubmitField, HiddenField
from wtforms.validators import DataRequired
import os
import uuid
from datetime import datetime

image_bp = Blueprint('image', __name__, url_prefix='/image')

class ImageUploadForm(FlaskForm):
    image = FileField('Image', validators=[
        FileRequired(),
        FileAllowed(['jpg', 'jpeg', 'png', 'gif'], 'Images only!')
    ])
    timeline_id = HiddenField('Timeline ID', validators=[DataRequired()])
    submit = SubmitField('Upload Image')

class ImageProcessForm(FlaskForm):
    ai_content = TextAreaField('AI Content')
    time_mark = TimeField('Time Mark', format='%H:%M')
    submit = SubmitField('Save')

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in {'png', 'jpg', 'jpeg', 'gif'}

def save_image(file):
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        # Generate a unique filename to prevent overwriting
        unique_filename = f"{uuid.uuid4().hex}_{filename}"
        upload_folder = current_app.config['UPLOAD_FOLDER']

        # Debug information
        print(f"Upload folder: {upload_folder}")
        print(f"File to save: {unique_filename}")

        # Ensure the upload folder exists
        os.makedirs(upload_folder, exist_ok=True)

        file_path = os.path.join(upload_folder, unique_filename)
        print(f"Full file path: {file_path}")

        try:
            file.save(file_path)
            print(f"File saved successfully at: {file_path}")
            return unique_filename
        except Exception as e:
            print(f"Error saving file: {str(e)}")
            return None
    return None

@image_bp.route('/upload', methods=['GET', 'POST'])
@login_required
def upload():
    form = ImageUploadForm()

    # Get timeline_id from query parameter
    timeline_id = request.args.get('timeline_id', type=int)
    if timeline_id:
        form.timeline_id.data = timeline_id

    if form.validate_on_submit():
        print(f"Form validated, timeline_id: {form.timeline_id.data}")
        timeline = Timeline.query.get_or_404(form.timeline_id.data)

        # Check if user is authorized to add images to this timeline
        if timeline.user_id != current_user.id and not current_user.is_admin:
            flash('You are not authorized to add images to this timeline', 'danger')
            return redirect(url_for('timeline.view', id=timeline.id))

        file = form.image.data
        print(f"Received file: {file.filename}, mimetype: {file.mimetype}")

        # Ensure the upload folder exists
        upload_folder = current_app.config['UPLOAD_FOLDER']
        os.makedirs(upload_folder, exist_ok=True)
        print(f"Upload folder: {upload_folder}")

        filename = save_image(file)
        print(f"Saved image filename: {filename}")

        if filename:
            # Create new image record
            image = Image(
                filename=filename,
                original_filename=secure_filename(file.filename),
                timeline_id=timeline.id,
                user_id=current_user.id
            )
            db.session.add(image)
            db.session.commit()
            print(f"Created image record with ID: {image.id}")

            # Verify the file exists
            file_path = os.path.join(upload_folder, filename)
            if os.path.exists(file_path):
                print(f"File exists at: {file_path}")
            else:
                print(f"WARNING: File does not exist at: {file_path}")

            flash('Image uploaded successfully!', 'success')
            return redirect(url_for('image.process', id=image.id))
        else:
            flash('Error saving image', 'danger')

    return render_template('image/upload.html', form=form)

@image_bp.route('/<int:id>/process', methods=['GET', 'POST'])
@login_required
def process(id):
    image = Image.query.get_or_404(id)

    # Check if user is authorized to process this image
    if image.user_id != current_user.id and not current_user.is_admin:
        flash('You are not authorized to process this image', 'danger')
        return redirect(url_for('timeline.view', id=image.timeline_id))

    form = ImageProcessForm()

    if form.validate_on_submit():
        # In a real application, this would call an AI service to process the image
        # For now, we'll just save the user-provided content

        image.ai_content = form.ai_content.data
        image.time_mark = form.time_mark.data
        db.session.commit()

        flash('Image processed successfully!', 'success')
        return redirect(url_for('timeline.view', id=image.timeline_id))

    # Fill form with existing data
    if request.method == 'GET' and image.ai_content:
        form.ai_content.data = image.ai_content
        form.time_mark.data = image.time_mark

    # For demo purposes, generate some mock AI content if none exists
    if not form.ai_content.data:
        form.ai_content.data = f"This is a screenshot taken on {datetime.now().strftime('%Y-%m-%d')}. It appears to show a work-related task."

    return render_template('image/process.html', form=form, image=image)

@image_bp.route('/<int:id>/generate-ai-content', methods=['POST'])
@login_required
def generate_ai_content(id):
    """Generate AI content for an image via AJAX request"""
    image = Image.query.get_or_404(id)

    # Check if user is authorized to process this image
    if image.user_id != current_user.id and not current_user.is_admin:
        return jsonify({'error': 'You are not authorized to process this image'}), 403

    try:
        # TODO: Replace this with actual AI service call
        # For now, generate mock AI content based on image metadata
        ai_content = f"""## Image Analysis

**Filename:** {image.original_filename}
**Upload Date:** {image.created_at.strftime('%Y-%m-%d %H:%M')}

### AI-Generated Description
This appears to be a screenshot or image related to work activities. The image was captured and uploaded to the timeline system for documentation purposes.

### Key Observations
- Image format appears to be suitable for timeline documentation
- Timestamp indicates when the image was captured
- Content suggests work-related activities or processes

### Recommendations
- Add specific time markers for better timeline organization
- Consider adding contextual descriptions for better understanding
- Link to related timeline events if applicable

*Note: This is a placeholder AI analysis. In production, this would be replaced with actual AI image analysis.*"""

        return jsonify({
            'success': True,
            'ai_content': ai_content
        })

    except Exception as e:
        return jsonify({
            'error': f'Failed to generate AI content: {str(e)}'
        }), 500

@image_bp.route('/<int:id>/delete', methods=['GET', 'POST'])
@login_required
def delete(id):
    print(f"Delete route called for image ID: {id}")
    image = Image.query.get_or_404(id)
    print(f"Found image: {image.filename}")

    # Check if user is authorized to delete this image
    if image.user_id != current_user.id and not current_user.is_admin:
        print(f"User not authorized: user_id={current_user.id}, image.user_id={image.user_id}")
        flash('You are not authorized to delete this image', 'danger')
        return redirect(url_for('timeline.view', id=image.timeline_id))

    timeline_id = image.timeline_id
    print(f"Timeline ID: {timeline_id}")

    # Delete the file from the filesystem
    try:
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], image.filename)
        print(f"Deleting file: {file_path}")
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"File deleted successfully")
        else:
            print(f"File does not exist: {file_path}")
    except Exception as e:
        print(f"Error deleting file: {str(e)}")
        flash(f'Error deleting image file: {str(e)}', 'warning')

    # Delete the database record
    print(f"Deleting image from database")
    db.session.delete(image)
    db.session.commit()
    print(f"Image deleted from database")

    flash('Image deleted successfully!', 'success')
    return redirect(url_for('timeline.view', id=timeline_id))
