from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app import db
from app.models.timeline import Timeline
from app.models.company import Company
from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, DateField, SubmitField, SelectField
from wtforms.validators import DataRequired
import markdown2
from datetime import datetime

timeline_bp = Blueprint('timeline', __name__, url_prefix='/timeline')

class TimelineForm(FlaskForm):
    title = StringField('Title', validators=[DataRequired()])
    work_date = DateField('Work Date', validators=[DataRequired()], format='%Y-%m-%d')
    description = TextAreaField('Description')
    business_content = TextAreaField('Business Content (Markdown)')
    company_id = SelectField('Company', coerce=int, validators=[DataRequired()])
    submit = SubmitField('Save Timeline')

class EvaluationForm(FlaskForm):
    business_content = TextAreaField('Business Content (Markdown)', validators=[DataRequired()])
    submit = SubmitField('Generate Evaluation')

@timeline_bp.route('/')
@login_required
def index():
    # Get filter parameters
    company_id = request.args.get('company_id', type=int)
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')

    # Base query
    query = Timeline.query

    # Apply filters
    if company_id:
        query = query.filter_by(company_id=company_id)
    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
            query = query.filter(Timeline.work_date >= date_from)
        except ValueError:
            flash('Invalid date format for Date From', 'warning')
    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(Timeline.work_date <= date_to)
        except ValueError:
            flash('Invalid date format for Date To', 'warning')

    # Get timelines
    timelines = query.order_by(Timeline.work_date.desc()).all()
    companies = Company.query.all()

    return render_template('timeline/index.html',
                          timelines=timelines,
                          companies=companies,
                          selected_company=company_id,
                          date_from=date_from,
                          date_to=date_to)

@timeline_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    form = TimelineForm()
    form.company_id.choices = [(c.id, c.name) for c in Company.query.order_by('name')]

    if form.validate_on_submit():
        timeline = Timeline(
            title=form.title.data,
            work_date=form.work_date.data,
            description=form.description.data,
            business_content=form.business_content.data,
            user_id=current_user.id,
            company_id=form.company_id.data
        )
        db.session.add(timeline)
        db.session.commit()
        flash('Timeline created successfully!', 'success')
        return redirect(url_for('timeline.view', id=timeline.id))

    # Pre-select user's company
    if current_user.company_id:
        form.company_id.data = current_user.company_id

    return render_template('timeline/create.html', form=form)

@timeline_bp.route('/<int:id>')
@login_required
def view(id):
    timeline = Timeline.query.get_or_404(id)
    # Convert markdown to HTML
    if timeline.business_content:
        business_html = markdown2.markdown(timeline.business_content)
    else:
        business_html = ""

    if timeline.evaluation_content:
        evaluation_html = markdown2.markdown(timeline.evaluation_content)
    else:
        evaluation_html = ""

    # Sort images by time_mark, handling None values properly
    # Images with None time_mark will be placed at the end
    sorted_images = sorted(timeline.images, key=lambda x: x.time_mark if x.time_mark is not None else datetime.max.time())

    return render_template('timeline/view.html',
                          timeline=timeline,
                          business_html=business_html,
                          evaluation_html=evaluation_html,
                          sorted_images=sorted_images)

@timeline_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    timeline = Timeline.query.get_or_404(id)

    # Check if user is authorized to edit this timeline
    if timeline.user_id != current_user.id and not current_user.is_admin:
        flash('You are not authorized to edit this timeline', 'danger')
        return redirect(url_for('timeline.view', id=id))

    form = TimelineForm()
    form.company_id.choices = [(c.id, c.name) for c in Company.query.order_by('name')]

    if form.validate_on_submit():
        timeline.title = form.title.data
        timeline.work_date = form.work_date.data
        timeline.description = form.description.data
        timeline.business_content = form.business_content.data
        timeline.company_id = form.company_id.data
        db.session.commit()
        flash('Timeline updated successfully!', 'success')
        return redirect(url_for('timeline.view', id=id))

    # Fill form with existing data
    if request.method == 'GET':
        form.title.data = timeline.title
        form.work_date.data = timeline.work_date
        form.description.data = timeline.description
        form.business_content.data = timeline.business_content
        form.company_id.data = timeline.company_id

    return render_template('timeline/edit.html', form=form, timeline=timeline)

@timeline_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    timeline = Timeline.query.get_or_404(id)

    # Check if user is authorized to delete this timeline
    if timeline.user_id != current_user.id and not current_user.is_admin:
        flash('You are not authorized to delete this timeline', 'danger')
        return redirect(url_for('timeline.view', id=id))

    db.session.delete(timeline)
    db.session.commit()
    flash('Timeline deleted successfully!', 'success')
    return redirect(url_for('timeline.index'))

@timeline_bp.route('/<int:id>/evaluate', methods=['GET', 'POST'])
@login_required
def evaluate(id):
    timeline = Timeline.query.get_or_404(id)

    # Check if user is authorized to evaluate this timeline
    if timeline.user_id != current_user.id and not current_user.is_admin:
        flash('You are not authorized to evaluate this timeline', 'danger')
        return redirect(url_for('timeline.view', id=id))

    form = EvaluationForm()

    if form.validate_on_submit():
        # In a real application, this would call an AI service to generate the evaluation
        # For now, we'll just create a simple evaluation based on the business content
        business_content = form.business_content.data
        timeline.business_content = business_content

        # Simple mock evaluation - in a real app, this would be AI-generated
        evaluation = f"""
## Evaluation of Work on {timeline.work_date.strftime('%Y-%m-%d')}

### Summary
This is an automated evaluation of the work described in the business content.

### Key Points
- The work appears to be well-documented
- Timeline entries provide good context
- Further details would enhance the evaluation

### Recommendations
- Continue maintaining detailed records
- Add more specific metrics where possible
- Link related work items for better context
        """

        timeline.evaluation_content = evaluation
        db.session.commit()
        flash('Evaluation generated successfully!', 'success')
        return redirect(url_for('timeline.view', id=id))

    # Fill form with existing data
    if request.method == 'GET':
        form.business_content.data = timeline.business_content

    return render_template('timeline/evaluate.html', form=form, timeline=timeline)
