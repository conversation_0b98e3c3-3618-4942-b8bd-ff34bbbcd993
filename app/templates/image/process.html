{% extends "base.html" %}

{% block title %}Process Image - Timeline Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-cog me-2"></i>Process Image</h1>
    <a href="{{ url_for('timeline.view', id=image.timeline_id) }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i> Back to Timeline
    </a>
</div>

<div class="row">
    <div class="col-md-5">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Image Preview</h4>
            </div>
            <div class="card-body text-center">
                <img src="{{ url_for('static', filename='uploads/' + image.filename) }}"
                     class="img-fluid rounded" alt="{{ image.original_filename }}">
                <div class="mt-3">
                    <h5>{{ image.original_filename }}</h5>
                    <p class="text-muted">
                        Uploaded on {{ image.created_at.strftime('%Y-%m-%d %H:%M') }}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-7">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <h4 class="mb-0">Process Image</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-4">
                    <i class="fas fa-robot me-2"></i> The AI has analyzed this image and generated content. You can edit this content and add a time mark.
                </div>

                <form method="post" action="{{ url_for('image.process', id=image.id) }}">
                    {{ form.hidden_tag() }}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            {{ form.ai_content.label(class="form-label mb-0") }}
                            <button type="button" id="generateAiContentBtn" class="btn btn-sm btn-success">
                                <i class="fas fa-robot me-1"></i> AI Gen Content
                            </button>
                        </div>
                        {{ form.ai_content(class="form-control", rows=8, placeholder="AI-generated content about this image", id="ai-content-textarea") }}
                        {% for error in form.ai_content.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        <div id="ai-loading" class="text-center mt-2" style="display: none;">
                            <div class="spinner-border spinner-border-sm text-success" role="status">
                                <span class="visually-hidden">Generating AI content...</span>
                            </div>
                            <small class="text-muted ms-2">Generating AI content...</small>
                        </div>
                    </div>
                    <div class="mb-4">
                        {{ form.time_mark.label(class="form-label") }}
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                            {{ form.time_mark(class="form-control", type="time") }}
                        </div>
                        <small class="text-muted">The time when this screenshot was taken</small>
                        {% for error in form.time_mark.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-info btn-lg") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const generateBtn = document.getElementById('generateAiContentBtn');
    const textarea = document.getElementById('ai-content-textarea');
    const loadingDiv = document.getElementById('ai-loading');

    generateBtn.addEventListener('click', function() {
        // Show loading state
        generateBtn.disabled = true;
        generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Generating...';
        loadingDiv.style.display = 'block';

        // Make AJAX request to generate AI content
        fetch('{{ url_for("image.generate_ai_content", id=image.id) }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update textarea with AI-generated content
                textarea.value = data.ai_content;

                // Show success message
                showAlert('AI content generated successfully!', 'success');
            } else {
                // Show error message
                showAlert(data.error || 'Failed to generate AI content', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('An error occurred while generating AI content', 'danger');
        })
        .finally(() => {
            // Reset button state
            generateBtn.disabled = false;
            generateBtn.innerHTML = '<i class="fas fa-robot me-1"></i> AI Gen Content';
            loadingDiv.style.display = 'none';
        });
    });

    function showAlert(message, type) {
        // Create alert element
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert alert at the top of the card body
        const cardBody = document.querySelector('.card-body');
        cardBody.insertBefore(alertDiv, cardBody.firstChild);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
});
</script>
{% endblock %}
