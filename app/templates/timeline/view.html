{% extends "base.html" %}

{% block title %}{{ timeline.title }} - Timeline Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-clock me-2"></i>{{ timeline.title }}</h1>
    <div>
        {% if current_user.id == timeline.user_id or current_user.is_admin %}
        <a href="{{ url_for('timeline.edit', id=timeline.id) }}" class="btn btn-warning me-2">
            <i class="fas fa-edit me-1"></i> Edit
        </a>
        <a href="{{ url_for('timeline.evaluate', id=timeline.id) }}" class="btn btn-info me-2">
            <i class="fas fa-chart-line me-1"></i> Evaluate
        </a>
        {% endif %}
        <a href="{{ url_for('timeline.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Timelines
        </a>
    </div>
</div>

<!-- Horizontal Timeline Section -->
<div class="card shadow mb-4">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h4 class="mb-0">Visual Timeline</h4>
        <a href="{{ url_for('image.upload', timeline_id=timeline.id) }}" class="btn btn-sm btn-light">
            <i class="fas fa-upload me-1"></i> Add Image
        </a>
    </div>
    <div class="card-body">
        {% if timeline.images.count() > 0 %}
            <!-- Timeline visualization -->
            <div class="timeline-container mb-4">
                <div class="timeline-track">
                    <div class="timeline-images">
                        {% for image in sorted_images %}
                            <div class="timeline-item">
                                <div class="timeline-image-container">
                                    <img src="{{ url_for('static', filename='uploads/' + image.filename) }}"
                                         class="timeline-image" alt="{{ image.original_filename }}">
                                    <div class="timeline-image-actions">
                                        <a href="{{ url_for('image.process', id=image.id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-cog"></i>
                                        </a>
                                        {% if current_user.id == image.user_id or current_user.is_admin %}
                                        <a href="{{ url_for('image.delete', id=image.id) }}" class="btn btn-sm btn-danger delete-image-btn"
                                           onclick="return confirm('Are you sure you want to delete this image?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="timeline-marker">
                                    <div class="timeline-time">
                                        {% if image.time_mark %}
                                            <span class="badge bg-primary">{{ image.time_mark.strftime('%H:%M') }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">No time</span>
                                        {% endif %}
                                    </div>
                                    <div class="timeline-label">
                                        <small class="text-truncate d-block" title="{{ image.original_filename }}">
                                            {{ image.original_filename|truncate(15) }}
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- No modal needed anymore -->
                        {% endfor %}
                    </div>
                    <div class="timeline-line"></div>
                </div>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-images fa-3x text-muted mb-3"></i>
                <p>No images uploaded yet.</p>
                <a href="{{ url_for('image.upload', timeline_id=timeline.id) }}" class="btn btn-primary">
                    <i class="fas fa-upload me-1"></i> Upload First Image
                </a>
            </div>
        {% endif %}
    </div>
</div>

<div class="row">
    <!-- Timeline Details -->
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Timeline Details</h4>
                <span class="badge bg-light text-dark">{{ timeline.work_date.strftime('%Y-%m-%d') }}</span>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h5>Description</h5>
                    <p>{{ timeline.description or 'No description provided.' }}</p>
                </div>

                <div class="mb-4">
                    <h5>Business Content</h5>
                    <div class="p-3 bg-light rounded">
                        {% if business_html %}
                            <div class="markdown-content">{{ business_html|safe }}</div>
                        {% else %}
                            <p class="text-muted">No business content provided.</p>
                        {% endif %}
                    </div>
                </div>

                {% if evaluation_html %}
                <div class="mb-4">
                    <h5>Evaluation</h5>
                    <div class="p-3 bg-light rounded">
                        <div class="markdown-content">{{ evaluation_html|safe }}</div>
                    </div>
                </div>
                {% endif %}

                <div class="mb-0">
                    <h5>Metadata</h5>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Company:</span>
                            <span class="badge bg-primary rounded-pill">{{ timeline.company.name }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Created By:</span>
                            <span>{{ timeline.user.username }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Created At:</span>
                            <span>{{ timeline.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Last Updated:</span>
                            <span>{{ timeline.updated_at.strftime('%Y-%m-%d %H:%M') }}</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Images Gallery Section -->
    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header bg-info text-white">
                <h4 class="mb-0">Image Gallery</h4>
            </div>
            <div class="card-body">
                {% if timeline.images.count() > 0 %}
                    <div class="row g-3">
                        {% for image in sorted_images %}
                        <div class="col-6">
                            <div class="card h-100">
                                <img src="{{ url_for('static', filename='uploads/' + image.filename) }}"
                                     class="card-img-top" alt="{{ image.original_filename }}">
                                <div class="card-body p-2">
                                    <h6 class="card-title text-truncate" title="{{ image.original_filename }}">
                                        {{ image.original_filename }}
                                    </h6>
                                    {% if image.time_mark %}
                                    <p class="card-text small mb-1">
                                        <i class="fas fa-clock me-1"></i> {{ image.time_mark.strftime('%H:%M') }}
                                    </p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">No images uploaded yet.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}