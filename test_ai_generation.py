#!/usr/bin/env python3
"""
Test script for AI content generation functionality
"""

import requests
import json

def test_ai_generation():
    """Test the AI content generation endpoint"""
    
    # Test configuration
    base_url = "http://127.0.0.1:5000"
    image_id = 5  # Using image ID 5 as seen in the logs
    
    print("Testing AI Content Generation Endpoint")
    print("=" * 50)
    
    # First, get the process page to obtain CSRF token
    process_url = f"{base_url}/image/{image_id}/process"
    print(f"1. Getting CSRF token from: {process_url}")
    
    session = requests.Session()
    response = session.get(process_url)
    
    if response.status_code != 200:
        print(f"❌ Failed to get process page: {response.status_code}")
        return False
    
    # Extract CSRF token from the response
    # In a real test, you'd parse the HTML to get the token
    # For now, we'll simulate the request without CSRF for testing
    print("✅ Process page loaded successfully")
    
    # Test the AI generation endpoint
    ai_gen_url = f"{base_url}/image/{image_id}/generate-ai-content"
    print(f"2. Testing AI generation endpoint: {ai_gen_url}")
    
    # Make the POST request
    headers = {
        'Content-Type': 'application/json',
        # Note: In production, you'd need to include the CSRF token
    }
    
    response = session.post(ai_gen_url, headers=headers)
    
    print(f"Response Status: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            if data.get('success'):
                print("✅ AI content generation successful!")
                print("\nGenerated Content Preview:")
                print("-" * 30)
                content = data.get('ai_content', '')
                # Show first 200 characters
                preview = content[:200] + "..." if len(content) > 200 else content
                print(preview)
                return True
            else:
                print(f"❌ AI generation failed: {data.get('error', 'Unknown error')}")
                return False
        except json.JSONDecodeError:
            print("❌ Invalid JSON response")
            return False
    else:
        print(f"❌ Request failed with status: {response.status_code}")
        print(f"Response: {response.text}")
        return False

def test_endpoint_availability():
    """Test if the Flask app is running and endpoints are available"""
    
    base_url = "http://127.0.0.1:5000"
    
    print("Testing Endpoint Availability")
    print("=" * 50)
    
    # Test main timeline page
    try:
        response = requests.get(f"{base_url}/timeline/", timeout=5)
        if response.status_code == 200:
            print("✅ Timeline index page is accessible")
        else:
            print(f"⚠️  Timeline index returned: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to timeline index: {e}")
        return False
    
    # Test specific timeline page
    try:
        response = requests.get(f"{base_url}/timeline/3", timeout=5)
        if response.status_code == 200:
            print("✅ Timeline view page is accessible")
        else:
            print(f"⚠️  Timeline view returned: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to timeline view: {e}")
    
    # Test image process page
    try:
        response = requests.get(f"{base_url}/image/5/process", timeout=5)
        if response.status_code == 200:
            print("✅ Image process page is accessible")
        else:
            print(f"⚠️  Image process returned: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to image process: {e}")
    
    return True

if __name__ == "__main__":
    print("Timeline Manager - AI Generation Test")
    print("=" * 60)
    
    # Test if endpoints are available
    if test_endpoint_availability():
        print("\n")
        # Test AI generation functionality
        test_ai_generation()
    
    print("\n" + "=" * 60)
    print("Test completed!")
    print("\nNote: For full functionality testing, use the web interface")
    print("and click the 'AI Gen Content' button on the process image page.")
